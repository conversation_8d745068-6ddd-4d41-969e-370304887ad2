"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/llm/generate/route";
exports.ids = ["app/api/llm/generate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/llm/generate/route.ts */ \"(rsc)/./src/app/api/llm/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/llm/generate/route\",\n        pathname: \"/api/llm/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/llm/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\api\\\\llm\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/llm/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/llm/generate/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/llm/generate/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llmProviders */ \"(rsc)/./src/lib/llmProviders.ts\");\n\n\n/**\n * API للتوليد الذكي باستخدام مقدمي خدمات LLM المختلفين\n */ async function POST(request) {\n    try {\n        const { providerId, apiKey, model, messages, context, fieldName, language = \"ar\", temperature = 0.7, maxTokens = 1000, baseUrl } = await request.json();\n        if (!providerId || !apiKey || !messages) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID, API key, and messages are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderById)(providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unknown provider\"\n            }, {\n                status: 400\n            });\n        }\n        const finalBaseUrl = baseUrl || provider.baseUrl;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...(0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderHeaders)(providerId)\n        };\n        // إنشاء system prompt ذكي بناءً على السياق\n        const systemPrompt = createSmartSystemPrompt(context, fieldName, language);\n        const finalMessages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...messages\n        ];\n        let response;\n        switch(providerId){\n            case \"openai\":\n            case \"openrouter\":\n            case \"deepseek\":\n            case \"groq\":\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"anthropic\":\n                response = await generateAnthropic(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"google\":\n                response = await generateGoogle(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            default:\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Generation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createSmartSystemPrompt(context, fieldName, language) {\n    const isArabic = language === \"ar\";\n    const basePrompt = isArabic ? `أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.` : `You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.`;\n    const contextInfo = isArabic ? `السياق الحالي للمشروع: ${JSON.stringify(context, null, 2)}` : `Current project context: ${JSON.stringify(context, null, 2)}`;\n    const fieldGuidance = getFieldGuidance(fieldName, isArabic);\n    const instructions = isArabic ? `\nتعليمات مهمة:\n1. قدم إجابة مفيدة ومنظمة ومفصلة\n2. استخدم المعلومات المتوفرة في السياق لتحسين إجابتك\n3. اجعل الإجابة عملية وقابلة للتطبيق\n4. استخدم اللغة العربية الواضحة والمهنية\n5. نظم الإجابة في نقاط أو فقرات حسب الحاجة\n6. تأكد من أن الإجابة تتناسب مع طبيعة المشروع المذكور في السياق\n` : `\nImportant instructions:\n1. Provide a helpful, organized, and detailed response\n2. Use the available context information to improve your answer\n3. Make the response practical and actionable\n4. Use clear and professional English\n5. Organize the response in points or paragraphs as needed\n6. Ensure the response fits the nature of the project mentioned in the context\n`;\n    return `${basePrompt}\\n\\n${contextInfo}\\n\\n${fieldGuidance}\\n\\n${instructions}`;\n}\nfunction getFieldGuidance(fieldName, isArabic) {\n    const fieldGuidanceMap = {\n        name: {\n            ar: \"المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع\",\n            en: \"Required field: Project name - Provide suggestions for creative and suitable project names\"\n        },\n        purpose: {\n            ar: \"المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة\",\n            en: \"Required field: Project purpose - Explain the main goal and added value\"\n        },\n        targetUsers: {\n            ar: \"المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة\",\n            en: \"Required field: Target users - Define the target audience precisely\"\n        },\n        goals: {\n            ar: \"المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس\",\n            en: \"Required field: Goals - Define clear and measurable objectives\"\n        },\n        scope: {\n            ar: \"المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع\",\n            en: \"Required field: Project scope - Define project boundaries and capabilities\"\n        },\n        timeline: {\n            ar: \"المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية\",\n            en: \"Required field: Timeline - Suggest a realistic time plan\"\n        },\n        programmingLanguages: {\n            ar: \"المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع\",\n            en: \"Required field: Programming languages - Suggest the best programming languages for the project\"\n        },\n        frameworks: {\n            ar: \"المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات\",\n            en: \"Required field: Frameworks - Suggest the best frameworks and libraries\"\n        },\n        databases: {\n            ar: \"المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات\",\n            en: \"Required field: Databases - Suggest the most suitable databases\"\n        }\n    };\n    const guidance = fieldGuidanceMap[fieldName];\n    if (guidance) {\n        return isArabic ? guidance.ar : guidance.en;\n    }\n    return isArabic ? `المجال المطلوب: ${fieldName} - قدم محتوى مفيد ومناسب لهذا المجال` : `Required field: ${fieldName} - Provide helpful and appropriate content for this field`;\n}\nasync function generateOpenAICompatible(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        const response = await fetch(`${baseUrl}/chat/completions`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                messages,\n                temperature,\n                max_tokens: maxTokens\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.choices?.[0]?.message?.content || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateAnthropic(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // Convert OpenAI format to Anthropic format\n        const anthropicMessages = messages.filter((m)=>m.role !== \"system\");\n        const systemMessage = messages.find((m)=>m.role === \"system\")?.content || \"\";\n        const response = await fetch(`${baseUrl}/messages`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"x-api-key\": apiKey\n            },\n            body: JSON.stringify({\n                model,\n                max_tokens: maxTokens,\n                temperature,\n                system: systemMessage,\n                messages: anthropicMessages\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.content?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateGoogle(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // Convert to Google format\n        const contents = messages.map((msg)=>({\n                role: msg.role === \"assistant\" ? \"model\" : \"user\",\n                parts: [\n                    {\n                        text: msg.content\n                    }\n                ]\n            }));\n        const response = await fetch(`${baseUrl}/models/${model}:generateContent?key=${apiKey}`, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                contents,\n                generationConfig: {\n                    temperature,\n                    maxOutputTokens: maxTokens\n                }\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.candidates?.[0]?.content?.parts?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usageMetadata\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/llm/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Advanced reasoning with 2M context\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.headers || {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llmProviders.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();