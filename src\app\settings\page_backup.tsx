'use client';

import { useState, useEffect } from 'react';
import { useContextStore, ProviderConfig } from '@/store/contextStore';
import { LLM_PROVIDERS_DATABASE, getProviderById } from '@/lib/llmProviders';
import { 
  Settings, 
  Eye, 
  EyeOff, 
  Plus, 
  Trash2, 
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Loader2,
  TestTube,
  Database,
  Save,
  ChevronDown,
  Wand2
} from 'lucide-react';
import Link from 'next/link';
import ThemeToggle from '@/components/ThemeToggle';
import LanguageToggle from '@/components/LanguageToggle';
import TestAIGeneration from '@/components/TestAIGeneration';

interface ValidationState {
  [providerId: string]: {
    status: 'idle' | 'validating' | 'valid' | 'invalid' | 'error';
    message?: string;
    lastValidated?: Date;
  };
}

export default function SettingsPage() {
  const {
    currentLanguage,
    apiSettings,
    setApiSettings,
    addProvider,
    updateProvider,
    removeProvider,
    validateProvider,
    getProvider,
    getActiveProviders
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [validationStates, setValidationStates] = useState<ValidationState>({});
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [expandedProviders, setExpandedProviders] = useState<{[key: string]: boolean}>({});
  const [selectedModels, setSelectedModels] = useState<{[key: string]: string[]}>({});
  const [newCustomModel, setNewCustomModel] = useState('');

  // تهيئة النماذج المحددة عند تحميل الصفحة
  useEffect(() => {
    const configuredProviders = apiSettings.providers || [];
    const initialModels: {[key: string]: string[]} = {};
    configuredProviders.forEach(provider => {
      initialModels[provider.id] = provider.selectedModels || [];
    });
    setSelectedModels(initialModels);
  }, [apiSettings.providers]);

  const translations = {
    title: isArabic ? 'إعدادات نماذج الذكاء الاصطناعي' : 'LLM API Settings',
    subtitle: isArabic ? 'قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة' : 'Configure your API keys and AI models',
    providers: isArabic ? 'مقدمو الخدمة' : 'LLM Providers',
    addProvider: isArabic ? 'إضافة مقدم خدمة' : 'Add Provider',
    apiKey: isArabic ? 'مفتاح API' : 'API Key',
    baseUrl: isArabic ? 'الرابط الأساسي' : 'Base URL',
    testConnection: isArabic ? 'اختبار الاتصال' : 'Test Connection',
    save: isArabic ? 'حفظ' : 'Save',
    delete: isArabic ? 'حذف' : 'Delete',
    validating: isArabic ? 'جاري التحقق...' : 'Validating...',
    valid: isArabic ? 'صالح' : 'Valid',
    invalid: isArabic ? 'غير صالح' : 'Invalid',
    error: isArabic ? 'خطأ' : 'Error',
    models: isArabic ? 'النماذج المتاحة' : 'Available Models',
    selectedModels: isArabic ? 'النماذج المحددة' : 'Selected Models',
    addCustomModel: isArabic ? 'إضافة نموذج مخصص' : 'Add Custom Model',
    customModelName: isArabic ? 'اسم النموذج المخصص' : 'Custom Model Name',
    editModels: isArabic ? 'تعديل النماذج' : 'Edit Models',
    saveModels: isArabic ? 'حفظ النماذج' : 'Save Models',
    removeModel: isArabic ? 'إزالة النموذج' : 'Remove Model',
    noModelsSelected: isArabic ? 'لم يتم تحديد أي نماذج' : 'No models selected',
    selectProvider: isArabic ? 'اختر مقدم الخدمة' : 'Select Provider',
    cancel: isArabic ? 'إلغاء' : 'Cancel',
    add: isArabic ? 'إضافة' : 'Add',
    backToHome: isArabic ? 'العودة للرئيسية' : 'Back to Home'
  };

  // دوال إدارة النماذج
  const toggleModelSelection = (providerId: string, modelId: string) => {
    setSelectedModels(prev => {
      const currentModels = prev[providerId] || [];
      const isSelected = currentModels.includes(modelId);
      
      return {
        ...prev,
        [providerId]: isSelected 
          ? currentModels.filter(id => id !== modelId)
          : [...currentModels, modelId]
      };
    });
  };

  const addCustomModel = (providerId: string) => {
    if (newCustomModel.trim()) {
      setSelectedModels(prev => {
        const currentModels = prev[providerId] || [];
        return {
          ...prev,
          [providerId]: [...currentModels, newCustomModel.trim()]
        };
      });
      setNewCustomModel('');
    }
  };

  const removeCustomModel = (providerId: string, modelId: string) => {
    setSelectedModels(prev => {
      const currentModels = prev[providerId] || [];
      return {
        ...prev,
        [providerId]: currentModels.filter(id => id !== modelId)
      };
    });
  };

  const saveProviderModels = (providerId: string) => {
    const models = selectedModels[providerId] || [];
    updateProvider(providerId, { selectedModels: models });
    setExpandedProviders(prev => ({ ...prev, [providerId]: false }));
  };

  const handleAddProvider = async () => {
    setErrorMessage('');

    if (!selectedProviderId) {
      setErrorMessage(isArabic ? 'يرجى اختيار مقدم خدمة' : 'Please select a provider');
      return;
    }

    const providerTemplate = getProviderById(selectedProviderId);
    if (!providerTemplate) {
      setErrorMessage(isArabic ? 'مقدم الخدمة غير موجود' : 'Provider not found');
      return;
    }

    const existingProvider = getProvider(selectedProviderId);
    if (existingProvider) {
      setErrorMessage(isArabic ? 'مقدم الخدمة موجود بالفعل' : 'Provider already exists');
      setShowAddProvider(false);
      setSelectedProviderId('');
      return;
    }

    try {
      const newProvider: ProviderConfig = {
        id: selectedProviderId,
        apiKey: '',
        selectedModels: [],
        isEnabled: false,
        validationStatus: 'pending'
      };

      addProvider(newProvider);
      setShowAddProvider(false);
      setSelectedProviderId('');
      setErrorMessage('');
    } catch (error) {
      console.error('Error adding provider:', error);
      setErrorMessage(isArabic ? 'حدث خطأ أثناء إضافة مقدم الخدمة' : 'Error adding provider');
    }
  };

  const handleValidateProvider = async (providerId: string) => {
    setValidationStates(prev => ({
      ...prev,
      [providerId]: { status: 'validating' }
    }));

    try {
      const isValid = await validateProvider(providerId);
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: isValid ? 'valid' : 'invalid',
          message: isValid ? translations.valid : translations.invalid,
          lastValidated: new Date()
        }
      }));
    } catch (error) {
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: 'error',
          message: error instanceof Error ? error.message : translations.error
        }
      }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'validating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'invalid':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const configuredProviders = apiSettings.providers || [];
  const availableProviders = LLM_PROVIDERS_DATABASE.filter(
    p => !configuredProviders.some(cp => cp.id === p.id)
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/" 
                className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-arabic">{translations.backToHome}</span>
              </Link>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
                    {translations.title}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                    {translations.subtitle}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <LanguageToggle />
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          
          {/* Providers Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic">
                  {translations.providers}
                </h2>
                <button
                  onClick={() => {
                    setShowAddProvider(true);
                    setErrorMessage('');
                    setSelectedProviderId('');
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic"
                >
                  <Plus className="w-4 h-4" />
                  {translations.addProvider}
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {configuredProviders.length === 0 ? (
                <div className="text-center py-8">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 font-arabic">
                    {isArabic ? 'لم يتم إعداد أي مقدم خدمة بعد' : 'No providers configured yet'}
                  </p>
                </div>
              ) : (
                configuredProviders.map((provider) => {
                  const providerInfo = getProviderById(provider.id);
                  const validationState = validationStates[provider.id];
                  
                  if (!providerInfo) return null;

                  return (
                    <div key={provider.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{providerInfo.icon}</span>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {providerInfo.name}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {providerInfo.description}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {getStatusIcon(validationState?.status || 'idle')}
                          <button
                            onClick={() => removeProvider(provider.id)}
                            className="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      <div className="space-y-4">
                        {/* API Key Section */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              {translations.apiKey}
                            </label>
                            <div className="relative">
                              <input
                                type={showKeys[provider.id] ? 'text' : 'password'}
                                value={provider.apiKey}
                                onChange={(e) => updateProvider(provider.id, { apiKey: e.target.value })}
                                placeholder={providerInfo.apiKeyPlaceholder}
                                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                              <button
                                onClick={() => setShowKeys(prev => ({ ...prev, [provider.id]: !prev[provider.id] }))}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                              >
                                {showKeys[provider.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              {translations.baseUrl}
                            </label>
                            <input
                              type="text"
                              value={provider.baseUrl || providerInfo.baseUrl}
                              onChange={(e) => updateProvider(provider.id, { baseUrl: e.target.value })}
                              placeholder={providerInfo.baseUrl}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {translations.models}: {providerInfo.models.length}
                            </span>
                          </div>
                          
                          <button
                            onClick={() => handleValidateProvider(provider.id)}
                            disabled={!provider.apiKey || validationState?.status === 'validating'}
                            className="flex items-center gap-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                          >
                            <TestTube className="w-4 h-4" />
                            {validationState?.status === 'validating' ? translations.validating : translations.testConnection}
                          </button>
                        </div>

                        {validationState?.message && (
                          <div className={`mt-2 p-2 rounded text-sm ${
                            validationState.status === 'valid' 
                              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                          }`}>
                            {validationState.message}
                          </div>
                        )}

                        {/* Models Management Section */}
                        <div className="mt-4 border-t border-gray-200 dark:border-gray-600 pt-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white font-arabic">
                              {translations.selectedModels}
                            </h4>
                            <button
                              onClick={() => {
                                setExpandedProviders(prev => ({ ...prev, [provider.id]: !prev[provider.id] }));
                                if (!selectedModels[provider.id]) {
                                  setSelectedModels(prev => ({ ...prev, [provider.id]: provider.selectedModels || [] }));
                                }
                              }}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic"
                            >
                              {expandedProviders[provider.id] ? translations.saveModels : translations.editModels}
                            </button>
                          </div>

                          {/* Current Selected Models Display */}
                          <div className="mb-3">
                            {(provider.selectedModels || []).length === 0 ? (
                              <p className="text-sm text-gray-500 dark:text-gray-400 font-arabic">
                                {translations.noModelsSelected}
                              </p>
                            ) : (
                              <div className="flex flex-wrap gap-2">
                                {(provider.selectedModels || []).map((modelId) => (
                                  <span
                                    key={modelId}
                                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic"
                                  >
                                    {modelId}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Expanded Models Management */}
                          {expandedProviders[provider.id] && (
                            <div className="space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              {/* Available Models */}
                              <div>
                                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic">
                                  {translations.models}
                                </h5>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                                  {providerInfo.models.map((model) => (
                                    <label
                                      key={model.id}
                                      className="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                                    >
                                      <input
                                        type="checkbox"
                                        checked={(selectedModels[provider.id] || []).includes(model.id)}
                                        onChange={() => toggleModelSelection(provider.id, model.id)}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                      />
                                      <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                          {model.name}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                          {model.description}
                                        </p>
                                      </div>
                                    </label>
                                  ))}
                                </div>
                              </div>

                              {/* Custom Model Input */}
                              <div>
                                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic">
                                  {translations.addCustomModel}
                                </h5>
                                <div className="flex gap-2">
                                  <input
                                    type="text"
                                    value={newCustomModel}
                                    onChange={(e) => setNewCustomModel(e.target.value)}
                                    placeholder={translations.customModelName}
                                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                                    onKeyPress={(e) => e.key === 'Enter' && addCustomModel(provider.id)}
                                  />
                                  <button
                                    onClick={() => addCustomModel(provider.id)}
                                    disabled={!newCustomModel.trim()}
                                    className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                                  >
                                    <Plus className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>

                              {/* Selected Models with Remove Option */}
                              {(selectedModels[provider.id] || []).length > 0 && (
                                <div>
                                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic">
                                    {translations.selectedModels}
                                  </h5>
                                  <div className="flex flex-wrap gap-2">
                                    {(selectedModels[provider.id] || []).map((modelId) => (
                                      <div
                                        key={modelId}
                                        className="flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs"
                                      >
                                        <span className="font-arabic">{modelId}</span>
                                        <button
                                          onClick={() => removeCustomModel(provider.id, modelId)}
                                          className="text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400"
                                        >
                                          <Trash2 className="w-3 h-3" />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Save Button */}
                              <div className="flex justify-end">
                                <button
                                  onClick={() => saveProviderModels(provider.id)}
                                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic"
                                >
                                  {translations.saveModels}
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* AI Generation Test Section */}
          <TestAIGeneration />
        </div>
      </div>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic">
              {translations.addProvider}
            </h3>
            
            <div className="space-y-4">
              <select
                value={selectedProviderId}
                onChange={(e) => setSelectedProviderId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">{isArabic ? 'اختر مقدم الخدمة' : 'Select Provider'}</option>
                {availableProviders.map(provider => (
                  <option key={provider.id} value={provider.id}>
                    {provider.icon} {provider.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Error Message */}
            {errorMessage && (
              <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <p className="text-sm text-red-700 dark:text-red-300 font-arabic">
                    {errorMessage}
                  </p>
                </div>
              </div>
            )}

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => {
                  setShowAddProvider(false);
                  setErrorMessage('');
                  setSelectedProviderId('');
                }}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                {isArabic ? 'إلغاء' : 'Cancel'}
              </button>
              <button
                onClick={handleAddProvider}
                disabled={!selectedProviderId}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isArabic ? 'إضافة' : 'Add'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
