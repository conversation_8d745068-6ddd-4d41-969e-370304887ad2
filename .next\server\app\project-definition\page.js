/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/project-definition/page";
exports.ids = ["app/project-definition/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'project-definition',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/project-definition/page.tsx */ \"(rsc)/./src/app/project-definition/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/project-definition/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/project-definition/page\",\n        pathname: \"/project-definition\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDc3R5bGVzJTVDJTVDZ2xhc3MtZWZmZWN0cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzc5ZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFRoZW1lUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/project-definition/page.tsx */ \"(ssr)/./src/app/project-definition/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9qZWN0LWRlZmluaXRpb24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWtIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8/ZjZlYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHByb2plY3QtZGVmaW5pdGlvblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDefinition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(ssr)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(ssr)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(ssr)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ProjectDefinition() {\n    const { projectDefinition, updateProjectDefinition } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"name\",\n            question: \"What is the name of your AI project?\",\n            questionAr: \"ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟\",\n            placeholder: \"e.g., Smart Customer Support Bot, Content Generator AI, etc.\",\n            placeholderAr: \"مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.\",\n            type: \"text\",\n            aiSuggestion: \"Choose a clear, descriptive name that reflects your project's main function and target audience.\",\n            aiSuggestionAr: \"اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.\",\n            promptTemplate: 'Help me refine this AI project name: \"{answer}\". Suggest improvements for clarity and market appeal.'\n        },\n        {\n            id: \"purpose\",\n            question: \"What is the main purpose or problem your AI project aims to solve?\",\n            questionAr: \"ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟\",\n            placeholder: \"Describe the core problem you want to address...\",\n            placeholderAr: \"صف المشكلة الأساسية التي تريد معالجتها...\",\n            aiSuggestion: \"Focus on a specific, measurable problem. Avoid being too broad or vague.\",\n            aiSuggestionAr: \"ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.\",\n            promptTemplate: 'Analyze this problem statement for an AI project: \"{answer}\". Help me make it more specific and actionable.'\n        },\n        {\n            id: \"targetUsers\",\n            question: \"Who are the primary users or beneficiaries of this project?\",\n            questionAr: \"من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟\",\n            placeholder: \"e.g., Customer service teams, Content creators, Students, etc.\",\n            placeholderAr: \"مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.\",\n            aiSuggestion: \"Be specific about user demographics, roles, and their current pain points.\",\n            aiSuggestionAr: \"كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.\",\n            promptTemplate: 'Help me create detailed user personas for this target audience: \"{answer}\". Include their needs and challenges.'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Project Definition\",\n        titleAr: \"تعريف المشروع\",\n        subtitle: \"Define the scope, users, and goals of your AI project\",\n        subtitleAr: \"حدد نطاق مشروعك والمستخدمين والأهداف\",\n        emoji: \"\\uD83C\\uDFAF\",\n        moduleKey: \"project-definition\",\n        backLink: {\n            href: \"/\",\n            label: \"← Back to Home\",\n            labelAr: \"← العودة للرئيسية\"\n        },\n        nextLink: {\n            href: \"/context-map\",\n            label: \"Next: Context Map →\",\n            labelAr: \"التالي: خريطة السياق ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: projectDefinition,\n            moduleName: \"Project Definition\",\n            moduleNameAr: \"تعريف المشروع\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: projectDefinition[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/project-definition/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AutoSaveIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/AutoSaveIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutoSaveIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AutoSaveIndicator() {\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    // Simulate auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setSaving(true);\n            setTimeout(()=>{\n                setLastSaved(new Date());\n                setSaving(false);\n            }, 500);\n        }, 10000); // Auto-save every 10 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    if (!lastSaved && !saving) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2\",\n            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"جاري الحفظ...\" : \"Saving...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            isArabic ? \"تم الحفظ\" : \"Saved\",\n                            \" \",\n                            lastSaved?.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AutoSaveIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed top-4 ${isArabic ? \"left-4\" : \"right-4\"} z-50 flex gap-3`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"الصفحة الرئيسية\" : \"Home\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/settings\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"إعدادات API\" : \"API Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModuleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(ssr)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AutoSaveIndicator */ \"(ssr)/./src/components/AutoSaveIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModuleLayout({ title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `space-y-6 ${isArabic ? \"lg:order-2\" : \"lg:order-1\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-3\",\n                                                children: \"✍️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                children: isArabic ? \"الأسئلة الذكية\" : \"Smart Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `space-y-6 ${isArabic ? \"lg:order-1\" : \"lg:order-2\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: isArabic ? \"المخرجات المجمّعة\" : \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto\",\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: \"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"←\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? backLink.labelAr : backLink.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: [\n                                isArabic ? nextLink.labelAr : nextLink.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModuleLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OutputPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/OutputPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OutputPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OutputPanel({ moduleData, moduleName, moduleNameAr }) {\n    const { currentLanguage, outputFormat, setOutputFormat } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحويل البيانات إلى تنسيقات مختلفة\n    const generateMarkdown = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let markdown = `# ${title}\\n\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                markdown += `## ${formattedKey}\\n${value}\\n\\n`;\n            }\n        });\n        return markdown;\n    };\n    const generateHTML = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let html = `<div class=\"module-output\">\\n  <h1>${title}</h1>\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                html += `  <section>\\n    <h2>${formattedKey}</h2>\\n    <p>${value}</p>\\n  </section>\\n`;\n            }\n        });\n        html += \"</div>\";\n        return html;\n    };\n    const generateJSON = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        return JSON.stringify({\n            module: isArabic ? moduleNameAr : moduleName,\n            data: filteredData,\n            metadata: {\n                timestamp: new Date().toISOString(),\n                language: isArabic ? \"ar\" : \"en\",\n                version: \"1.0\"\n            }\n        }, null, 2);\n    };\n    const generateYAML = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        let yaml = `# ${isArabic ? moduleNameAr : moduleName}\\n`;\n        yaml += `# Generated: ${new Date().toISOString()}\\n\\n`;\n        Object.entries(filteredData).forEach(([key, value])=>{\n            const formattedKey = key.replace(/([A-Z])/g, \"_$1\").toLowerCase();\n            yaml += `${formattedKey}: |\\n`;\n            const lines = String(value || \"\").split(\"\\n\");\n            lines.forEach((line)=>{\n                yaml += `  ${line}\\n`;\n            });\n            yaml += \"\\n\";\n        });\n        return yaml;\n    };\n    const getCurrentOutput = ()=>{\n        switch(outputFormat){\n            case \"markdown\":\n                return generateMarkdown();\n            case \"html\":\n                return generateHTML();\n            case \"json\":\n                return generateJSON();\n            case \"yaml\":\n                return generateYAML();\n            default:\n                return generateMarkdown();\n        }\n    };\n    const handleCopyAll = async ()=>{\n        const output = getCurrentOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = getCurrentOutput();\n        const extensions = {\n            markdown: \"md\",\n            html: \"html\",\n            json: \"json\",\n            yaml: \"yml\"\n        };\n        const extension = extensions[outputFormat] || \"txt\";\n        const filename = `${moduleName.toLowerCase().replace(/\\s+/g, \"-\")}.${extension}`;\n        const mimeTypes = {\n            markdown: \"text/markdown\",\n            html: \"text/html\",\n            json: \"application/json\",\n            yaml: \"text/yaml\"\n        };\n        const mimeType = mimeTypes[outputFormat] || \"text/plain\";\n        const blob = new Blob([\n            output\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasData = Object.values(moduleData).some((value)=>value && typeof value === \"string\" && value.trim());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 flex-wrap gap-2\",\n                        children: [\n                            \"markdown\",\n                            \"html\",\n                            \"json\",\n                            \"yaml\"\n                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setOutputFormat(format),\n                                className: `px-3 py-1 text-sm rounded-lg transition-colors ${outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"}`,\n                                children: format.toUpperCase()\n                            }, format, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    hasData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyAll,\n                                className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"group-hover:scale-110 transition-transform duration-300\",\n                                                children: \"\\uD83D\\uDCBE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            isArabic ? \"تحميل\" : \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]\",\n                children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: `text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${isArabic ? \"text-right\" : \"text-left\"}`,\n                    children: getCurrentOutput()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl mb-2 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: isArabic ? \"ابدأ بالإجابة على الأسئلة لرؤية المخرجات\" : \"Start answering questions to see outputs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: [\n                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\"),\n                    outputFormat === \"yaml\" && (isArabic ? \"تنسيق YAML - جاهز للتكوين والنشر\" : \"YAML format - Ready for configuration and deployment\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OutputPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProgressIndicator({ currentModule }) {\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"project-definition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"context-map\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotional-tone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technical-layer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legal-risk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const getModuleProgress = (moduleData)=>{\n        const totalFields = Object.keys(moduleData).length;\n        const filledFields = Object.values(moduleData).filter((value)=>value && typeof value === \"string\" && value.trim()).length;\n        return totalFields > 0 ? filledFields / totalFields * 100 : 0;\n    };\n    const overallProgress = modules.reduce((total, module)=>{\n        return total + getModuleProgress(module.data);\n    }, 0) / modules.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                        children: isArabic ? \"تقدم المشروع\" : \"Project Progress\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            Math.round(overallProgress),\n                            \"% \",\n                            isArabic ? \"مكتمل\" : \"Complete\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                    style: {\n                        width: `${overallProgress}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                children: modules.map((module)=>{\n                    const progress = getModuleProgress(module.data);\n                    const isCurrent = currentModule === module.key;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: module.href,\n                        className: `p-3 rounded-lg border-2 transition-all hover:shadow-md ${isCurrent ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl mb-2\",\n                                    children: module.emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: isArabic ? module.nameAr : module.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `h-1 rounded-full transition-all duration-300 ${progress === 100 ? \"bg-green-500\" : \"bg-blue-500\"}`,\n                                        style: {\n                                            width: `${progress}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        Math.round(progress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, module.key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 flex justify-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/final-preview\",\n                        className: \"flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    overallProgress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (confirm(isArabic ? \"هل أنت متأكد من إعادة تعيين جميع البيانات؟\" : \"Are you sure you want to reset all data?\")) {\n                                // Reset functionality would go here\n                                window.location.reload();\n                            }\n                        },\n                        className: \"flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            isArabic ? \"إعادة تعيين\" : \"Reset\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm9ncmVzc0luZGljYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFdUQ7QUFNeEMsU0FBU0Msa0JBQWtCLEVBQUVDLGFBQWEsRUFBMEI7SUFDakYsTUFBTSxFQUNKQyxpQkFBaUIsRUFDakJDLFVBQVUsRUFDVkMsYUFBYSxFQUNiQyxjQUFjLEVBQ2RDLFNBQVMsRUFDVEMsZUFBZSxFQUNoQixHQUFHUixvRUFBZUE7SUFFbkIsTUFBTVMsV0FBV0Qsb0JBQW9CO0lBRXJDLE1BQU1FLFVBQVU7UUFDZDtZQUNFQyxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxPQUFPO1lBQ1BDLE1BQU1aO1lBQ05hLE1BQU07UUFDUjtRQUNBO1lBQ0VMLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsTUFBTVg7WUFDTlksTUFBTTtRQUNSO1FBQ0E7WUFDRUwsS0FBSztZQUNMQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxNQUFNVjtZQUNOVyxNQUFNO1FBQ1I7UUFDQTtZQUNFTCxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxPQUFPO1lBQ1BDLE1BQU1UO1lBQ05VLE1BQU07UUFDUjtRQUNBO1lBQ0VMLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsTUFBTVI7WUFDTlMsTUFBTTtRQUNSO0tBQ0Q7SUFFRCxNQUFNQyxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTUMsY0FBY0MsT0FBT0MsSUFBSSxDQUFDSCxZQUFZSSxNQUFNO1FBQ2xELE1BQU1DLGVBQWVILE9BQU9JLE1BQU0sQ0FBQ04sWUFBWU8sTUFBTSxDQUFDQyxDQUFBQSxRQUNwREEsU0FBUyxPQUFPQSxVQUFVLFlBQVlBLE1BQU1DLElBQUksSUFDaERMLE1BQU07UUFDUixPQUFPSCxjQUFjLElBQUksZUFBZ0JBLGNBQWUsTUFBTTtJQUNoRTtJQUVBLE1BQU1TLGtCQUFrQmxCLFFBQVFtQixNQUFNLENBQUMsQ0FBQ0MsT0FBT0M7UUFDN0MsT0FBT0QsUUFBUWIsa0JBQWtCYyxPQUFPaEIsSUFBSTtJQUM5QyxHQUFHLEtBQUtMLFFBQVFZLE1BQU07SUFFdEIscUJBQ0UsOERBQUNVO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUNYeEIsV0FBVyxpQkFBaUI7Ozs7OztrQ0FFL0IsOERBQUMwQjt3QkFBS0YsV0FBVTs7NEJBQ2JHLEtBQUtDLEtBQUssQ0FBQ1Q7NEJBQWlCOzRCQUFHbkIsV0FBVyxVQUFVOzs7Ozs7Ozs7Ozs7OzBCQUt6RCw4REFBQ3VCO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFDQ0MsV0FBVTtvQkFDVkssT0FBTzt3QkFBRUMsT0FBTyxDQUFDLEVBQUVYLGdCQUFnQixDQUFDLENBQUM7b0JBQUM7Ozs7Ozs7Ozs7OzBCQUsxQyw4REFBQ0k7Z0JBQUlDLFdBQVU7MEJBQ1p2QixRQUFROEIsR0FBRyxDQUFDLENBQUNUO29CQUNaLE1BQU1VLFdBQVd4QixrQkFBa0JjLE9BQU9oQixJQUFJO29CQUM5QyxNQUFNMkIsWUFBWXhDLGtCQUFrQjZCLE9BQU9wQixHQUFHO29CQUU5QyxxQkFDRSw4REFBQ2dDO3dCQUVDM0IsTUFBTWUsT0FBT2YsSUFBSTt3QkFDakJpQixXQUFXLENBQUMsdURBQXVELEVBQ2pFUyxZQUNJLG1EQUNBLHdGQUNMLENBQUM7a0NBRUYsNEVBQUNWOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWlCRixPQUFPakIsS0FBSzs7Ozs7OzhDQUM1Qyw4REFBQ2tCO29DQUFJQyxXQUFVOzhDQUNaeEIsV0FBV3NCLE9BQU9sQixNQUFNLEdBQUdrQixPQUFPbkIsSUFBSTs7Ozs7OzhDQUl6Qyw4REFBQ29CO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLDZDQUE2QyxFQUN2RFEsYUFBYSxNQUFNLGlCQUFpQixjQUNyQyxDQUFDO3dDQUNGSCxPQUFPOzRDQUFFQyxPQUFPLENBQUMsRUFBRUUsU0FBUyxDQUFDLENBQUM7d0NBQUM7Ozs7Ozs7Ozs7OzhDQUluQyw4REFBQ1Q7b0NBQUlDLFdBQVU7O3dDQUNaRyxLQUFLQyxLQUFLLENBQUNJO3dDQUFVOzs7Ozs7Ozs7Ozs7O3VCQXpCckJWLE9BQU9wQixHQUFHOzs7OztnQkE4QnJCOzs7Ozs7MEJBSUYsOERBQUNxQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNVO3dCQUNDM0IsTUFBSzt3QkFDTGlCLFdBQVU7OzBDQUVWLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBTzs7Ozs7OzRCQUN0QnhCLFdBQVcsc0JBQXNCOzs7Ozs7O29CQUduQ21CLGtCQUFrQixtQkFDakIsOERBQUNnQjt3QkFDQ0MsU0FBUzs0QkFDUCxJQUFJQyxRQUFRckMsV0FBVywrQ0FBK0MsNkNBQTZDO2dDQUNqSCxvQ0FBb0M7Z0NBQ3BDc0MsT0FBT0MsUUFBUSxDQUFDQyxNQUFNOzRCQUN4Qjt3QkFDRjt3QkFDQWhCLFdBQVU7OzBDQUVWLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBTzs7Ozs7OzRCQUN0QnhCLFdBQVcsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9jb21wb25lbnRzL1Byb2dyZXNzSW5kaWNhdG9yLnRzeD9mMTg3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlQ29udGV4dFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9jb250ZXh0U3RvcmUnO1xuXG5pbnRlcmZhY2UgUHJvZ3Jlc3NJbmRpY2F0b3JQcm9wcyB7XG4gIGN1cnJlbnRNb2R1bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2dyZXNzSW5kaWNhdG9yKHsgY3VycmVudE1vZHVsZSB9OiBQcm9ncmVzc0luZGljYXRvclByb3BzKSB7XG4gIGNvbnN0IHsgXG4gICAgcHJvamVjdERlZmluaXRpb24sIFxuICAgIGNvbnRleHRNYXAsIFxuICAgIGVtb3Rpb25hbFRvbmUsIFxuICAgIHRlY2huaWNhbExheWVyLCBcbiAgICBsZWdhbFJpc2ssXG4gICAgY3VycmVudExhbmd1YWdlIFxuICB9ID0gdXNlQ29udGV4dFN0b3JlKCk7XG4gIFxuICBjb25zdCBpc0FyYWJpYyA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJztcblxuICBjb25zdCBtb2R1bGVzID0gW1xuICAgIHtcbiAgICAgIGtleTogJ3Byb2plY3QtZGVmaW5pdGlvbicsXG4gICAgICBuYW1lOiAnUHJvamVjdCBEZWZpbml0aW9uJyxcbiAgICAgIG5hbWVBcjogJ9iq2LnYsdmK2YEg2KfZhNmF2LTYsdmI2LknLFxuICAgICAgZW1vamk6ICfwn46vJyxcbiAgICAgIGRhdGE6IHByb2plY3REZWZpbml0aW9uLFxuICAgICAgaHJlZjogJy9wcm9qZWN0LWRlZmluaXRpb24nXG4gICAgfSxcbiAgICB7XG4gICAgICBrZXk6ICdjb250ZXh0LW1hcCcsXG4gICAgICBuYW1lOiAnQ29udGV4dCBNYXAnLFxuICAgICAgbmFtZUFyOiAn2K7YsdmK2LfYqSDYp9mE2LPZitin2YInLFxuICAgICAgZW1vamk6ICfwn5e677iPJyxcbiAgICAgIGRhdGE6IGNvbnRleHRNYXAsXG4gICAgICBocmVmOiAnL2NvbnRleHQtbWFwJ1xuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnZW1vdGlvbmFsLXRvbmUnLFxuICAgICAgbmFtZTogJ0Vtb3Rpb25hbCBUb25lJyxcbiAgICAgIG5hbWVBcjogJ9in2YTZhtio2LHYqSDYp9mE2LnYp9i32YHZitipJyxcbiAgICAgIGVtb2ppOiAn4pyoJyxcbiAgICAgIGRhdGE6IGVtb3Rpb25hbFRvbmUsXG4gICAgICBocmVmOiAnL2Vtb3Rpb25hbC10b25lJ1xuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAndGVjaG5pY2FsLWxheWVyJyxcbiAgICAgIG5hbWU6ICdUZWNobmljYWwgTGF5ZXInLFxuICAgICAgbmFtZUFyOiAn2KfZhNi32KjZgtipINin2YTYqtmC2YbZitipJyxcbiAgICAgIGVtb2ppOiAn4pqZ77iPJyxcbiAgICAgIGRhdGE6IHRlY2huaWNhbExheWVyLFxuICAgICAgaHJlZjogJy90ZWNobmljYWwtbGF5ZXInXG4gICAgfSxcbiAgICB7XG4gICAgICBrZXk6ICdsZWdhbC1yaXNrJyxcbiAgICAgIG5hbWU6ICdMZWdhbCAmIFByaXZhY3knLFxuICAgICAgbmFtZUFyOiAn2KfZhNmC2KfZhtmI2YbZitipINmI2KfZhNiu2LXZiNi12YrYqScsXG4gICAgICBlbW9qaTogJ/CflJInLFxuICAgICAgZGF0YTogbGVnYWxSaXNrLFxuICAgICAgaHJlZjogJy9sZWdhbC1yaXNrJ1xuICAgIH1cbiAgXTtcblxuICBjb25zdCBnZXRNb2R1bGVQcm9ncmVzcyA9IChtb2R1bGVEYXRhOiBhbnkpID0+IHtcbiAgICBjb25zdCB0b3RhbEZpZWxkcyA9IE9iamVjdC5rZXlzKG1vZHVsZURhdGEpLmxlbmd0aDtcbiAgICBjb25zdCBmaWxsZWRGaWVsZHMgPSBPYmplY3QudmFsdWVzKG1vZHVsZURhdGEpLmZpbHRlcih2YWx1ZSA9PiBcbiAgICAgIHZhbHVlICYmIHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgJiYgdmFsdWUudHJpbSgpXG4gICAgKS5sZW5ndGg7XG4gICAgcmV0dXJuIHRvdGFsRmllbGRzID4gMCA/IChmaWxsZWRGaWVsZHMgLyB0b3RhbEZpZWxkcykgKiAxMDAgOiAwO1xuICB9O1xuXG4gIGNvbnN0IG92ZXJhbGxQcm9ncmVzcyA9IG1vZHVsZXMucmVkdWNlKCh0b3RhbCwgbW9kdWxlKSA9PiB7XG4gICAgcmV0dXJuIHRvdGFsICsgZ2V0TW9kdWxlUHJvZ3Jlc3MobW9kdWxlLmRhdGEpO1xuICB9LCAwKSAvIG1vZHVsZXMubGVuZ3RoO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNiBtYi04XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICB7aXNBcmFiaWMgPyAn2KrZgtiv2YUg2KfZhNmF2LTYsdmI2LknIDogJ1Byb2plY3QgUHJvZ3Jlc3MnfVxuICAgICAgICA8L2gzPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAge01hdGgucm91bmQob3ZlcmFsbFByb2dyZXNzKX0lIHtpc0FyYWJpYyA/ICfZhdmD2KrZhdmEJyA6ICdDb21wbGV0ZSd9XG4gICAgICAgIDwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogT3ZlcmFsbCBQcm9ncmVzcyBCYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBoLTIgbWItNlwiPlxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7b3ZlcmFsbFByb2dyZXNzfSVgIH19XG4gICAgICAgID48L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTW9kdWxlIFByb2dyZXNzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy01IGdhcC00XCI+XG4gICAgICAgIHttb2R1bGVzLm1hcCgobW9kdWxlKSA9PiB7XG4gICAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSBnZXRNb2R1bGVQcm9ncmVzcyhtb2R1bGUuZGF0YSk7XG4gICAgICAgICAgY29uc3QgaXNDdXJyZW50ID0gY3VycmVudE1vZHVsZSA9PT0gbW9kdWxlLmtleTtcbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAga2V5PXttb2R1bGUua2V5fVxuICAgICAgICAgICAgICBocmVmPXttb2R1bGUuaHJlZn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtbGcgYm9yZGVyLTIgdHJhbnNpdGlvbi1hbGwgaG92ZXI6c2hhZG93LW1kICR7XG4gICAgICAgICAgICAgICAgaXNDdXJyZW50IFxuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTAgZGFyazpiZy1ibHVlLTkwMC8yMCcgXG4gICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwIGRhcms6aG92ZXI6Ym9yZGVyLWdyYXktNTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgbWItMlwiPnttb2R1bGUuZW1vaml9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtpc0FyYWJpYyA/IG1vZHVsZS5uYW1lQXIgOiBtb2R1bGUubmFtZX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB7LyogTWluaSBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0xIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtMSByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgcHJvZ3Jlc3MgPT09IDEwMCA/ICdiZy1ncmVlbi01MDAnIDogJ2JnLWJsdWUtNTAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3Byb2dyZXNzfSVgIH19XG4gICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICB7TWF0aC5yb3VuZChwcm9ncmVzcyl9JVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBmbGV4IGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICA8YVxuICAgICAgICAgIGhyZWY9XCIvZmluYWwtcHJldmlld1wiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgID5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0xXCI+8J+Tizwvc3Bhbj5cbiAgICAgICAgICB7aXNBcmFiaWMgPyAn2KfZhNmF2LnYp9mK2YbYqSDYp9mE2YbZh9in2KbZitipJyA6ICdGaW5hbCBQcmV2aWV3J31cbiAgICAgICAgPC9hPlxuICAgICAgICBcbiAgICAgICAge292ZXJhbGxQcm9ncmVzcyA+IDAgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGNvbmZpcm0oaXNBcmFiaWMgPyAn2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINil2LnYp9iv2Kkg2KrYudmK2YrZhiDYrNmF2YrYuSDYp9mE2KjZitin2YbYp9iq2J8nIDogJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byByZXNldCBhbGwgZGF0YT8nKSkge1xuICAgICAgICAgICAgICAgIC8vIFJlc2V0IGZ1bmN0aW9uYWxpdHkgd291bGQgZ28gaGVyZVxuICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMVwiPvCflIQ8L3NwYW4+XG4gICAgICAgICAgICB7aXNBcmFiaWMgPyAn2KXYudin2K/YqSDYqti52YrZitmGJyA6ICdSZXNldCd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VDb250ZXh0U3RvcmUiLCJQcm9ncmVzc0luZGljYXRvciIsImN1cnJlbnRNb2R1bGUiLCJwcm9qZWN0RGVmaW5pdGlvbiIsImNvbnRleHRNYXAiLCJlbW90aW9uYWxUb25lIiwidGVjaG5pY2FsTGF5ZXIiLCJsZWdhbFJpc2siLCJjdXJyZW50TGFuZ3VhZ2UiLCJpc0FyYWJpYyIsIm1vZHVsZXMiLCJrZXkiLCJuYW1lIiwibmFtZUFyIiwiZW1vamkiLCJkYXRhIiwiaHJlZiIsImdldE1vZHVsZVByb2dyZXNzIiwibW9kdWxlRGF0YSIsInRvdGFsRmllbGRzIiwiT2JqZWN0Iiwia2V5cyIsImxlbmd0aCIsImZpbGxlZEZpZWxkcyIsInZhbHVlcyIsImZpbHRlciIsInZhbHVlIiwidHJpbSIsIm92ZXJhbGxQcm9ncmVzcyIsInJlZHVjZSIsInRvdGFsIiwibW9kdWxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJzcGFuIiwiTWF0aCIsInJvdW5kIiwic3R5bGUiLCJ3aWR0aCIsIm1hcCIsInByb2dyZXNzIiwiaXNDdXJyZW50IiwiYSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjb25maXJtIiwid2luZG93IiwibG9jYXRpb24iLCJyZWxvYWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProgressIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartFieldAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SmartFieldAssistant({ fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" }) {\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isArabic = currentLanguage === \"ar\";\n    const activeProviders = getActiveProviders();\n    const hasValidProvider = activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\");\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI أولاً\" : \"Please configure an AI provider first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        setGeneratedSuggestions([]);\n        setShowSuggestions(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider?.name, \"with model:\", provider?.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.8,\n                maxTokens: 500\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(`API Error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // تقسيم النتيجة إلى اقتراحات متعددة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                setGeneratedSuggestions(suggestions);\n                if (suggestions.length === 0) {\n                    setGeneratedSuggestions([\n                        isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\"\n                    ]);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            setGeneratedSuggestions([\n                `${translations.error}: ${errorMessage}`\n            ]);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        const fieldPrompts = {\n            name: {\n                ar: `اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:\n- قصيرة وسهلة التذكر\n- تعكس طبيعة المشروع\n- مناسبة للجمهور المستهدف\n- أصلية ومميزة`,\n                en: `Suggest 3 creative and suitable project names. The names should be:\n- Short and memorable\n- Reflect the project nature\n- Suitable for target audience\n- Original and distinctive`\n            },\n            purpose: {\n                ar: `اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:\n- واضحة ومباشرة\n- تركز على القيمة المضافة\n- تجذب المستخدمين المستهدفين\n- تميز المشروع عن المنافسين`,\n                en: `Write 3 different versions describing the project purpose. They should be:\n- Clear and direct\n- Focus on added value\n- Attract target users\n- Differentiate from competitors`\n            },\n            targetUsers: {\n                ar: `حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:\n- الخصائص الديموغرافية\n- الاحتياجات والتحديات\n- السلوكيات والتفضيلات`,\n                en: `Define 3 different target user groups. For each group mention:\n- Demographic characteristics\n- Needs and challenges\n- Behaviors and preferences`\n            },\n            goals: {\n                ar: `اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:\n- محددة وقابلة للقياس\n- قابلة للتحقيق وواقعية\n- مرتبطة بالجدول الزمني\n- تدعم رؤية المشروع`,\n                en: `Suggest 3 sets of goals (short, medium, long-term). They should be:\n- Specific and measurable\n- Achievable and realistic\n- Time-bound\n- Support project vision`\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? `اقترح 3 خيارات مختلفة لـ ${fieldName}` : `Suggest 3 different options for ${fieldName}`;\n        const contextInfo = isArabic ? `السياق الحالي للمشروع:\\n${JSON.stringify(context, null, 2)}\\n\\n` : `Current project context:\\n${JSON.stringify(context, null, 2)}\\n\\n`;\n        const currentValueInfo = currentValue ? isArabic ? `القيمة الحالية: ${currentValue}\\n\\n` : `Current value: ${currentValue}\\n\\n` : \"\";\n        const instructions = isArabic ? `تعليمات:\n1. قدم 3 اقتراحات مختلفة ومتنوعة\n2. رقم كل اقتراح (1، 2، 3)\n3. اجعل كل اقتراح في سطر منفصل\n4. استخدم اللغة العربية الواضحة\n5. اعتمد على السياق المتوفر لتحسين الاقتراحات` : `Instructions:\n1. Provide 3 different and diverse suggestions\n2. Number each suggestion (1, 2, 3)\n3. Put each suggestion on a separate line\n4. Use clear English\n5. Use the available context to improve suggestions`;\n        return `${contextInfo}${currentValueInfo}${basePrompt}\\n\\n${instructions}`;\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const useSuggestion = (suggestion)=>{\n        onValueChange(suggestion);\n        setShowSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: `\n          relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out\n          backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group\n          ${hasValidProvider ? `bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80\n               hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90\n               text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25\n               hover:scale-105 active:scale-95` : `bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500\n               cursor-not-allowed border-gray-300/30 dark:border-gray-600/30`}\n          ${isGenerating ? \"animate-pulse scale-105\" : \"\"}\n        `,\n                title: hasValidProvider ? translations.generateWithAI : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.generateWithAI\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            showSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                    children: translations.suggestions\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowSuggestions(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-3\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 animate-spin text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600 dark:text-gray-400 font-arabic\",\n                                        children: translations.generating\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this) : generatedSuggestions.length > 0 ? generatedSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 dark:text-white mb-3 font-arabic leading-relaxed\",\n                                            children: suggestion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>useSuggestion(suggestion),\n                                                    className: \"relative flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"w-3 h-3 group-hover:rotate-12 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                translations.useThis\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>copyToClipboard(suggestion, index),\n                                                    className: \"relative flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-1\",\n                                                            children: [\n                                                                copiedIndex === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-green-300 animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3 group-hover:scale-110 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                copiedIndex === index ? translations.copied : translations.copy\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                        children: translations.error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: generateSuggestions,\n                                        className: \"relative mt-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative\",\n                                                children: translations.tryAgain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this),\n                            generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-3 border-t border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: generateSuggestions,\n                                    className: \"relative w-full flex items-center justify-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 to-pink-600/80 hover:from-purple-600/90 hover:to-pink-700/90 text-white shadow-md hover:shadow-lg hover:shadow-purple-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this),\n                                                translations.regenerate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartFieldAssistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartQuestion)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SmartFieldAssistant */ \"(ssr)/./src/components/SmartFieldAssistant.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SmartQuestion({ id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handleSuggestionApply = ()=>{\n        if (aiSuggestion || aiSuggestionAr) {\n            const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;\n            if (suggestion && !value.trim()) {\n                onChange(suggestion);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: isArabic ? questionAr : question\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSuggestion(!showSuggestion),\n                                className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83E\\uDDE0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        fieldName: id,\n                        fieldValue: value,\n                        onValueChange: onChange,\n                        placeholder: isArabic ? placeholderAr : placeholder,\n                        className: \"flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                        children: \"\\uD83D\\uDCCE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500 mr-2\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-800 dark:text-blue-200\",\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none ${isArabic ? \"text-right\" : \"text-left\"}`,\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white ${isArabic ? \"text-right\" : \"text-left\"}`,\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handlePromptCopy,\n                    className: \"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartQuestion.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setTheme(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            if (theme === \"dark\") {\n                root.classList.add(\"dark\");\n            } else {\n                root.classList.remove(\"dark\");\n            }\n            localStorage.setItem(\"theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXVFO0FBU3ZFLE1BQU1JLDZCQUFlSixvREFBYUEsQ0FBK0JLO0FBRTFELFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFpQztJQUN2RSxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR04sK0NBQVFBLENBQVE7SUFDMUMsTUFBTSxDQUFDTyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBRXZDLDBEQUEwRDtJQUMxREQsZ0RBQVNBLENBQUM7UUFDUixNQUFNVSxhQUFhQyxhQUFhQyxPQUFPLENBQUM7UUFDeEMsTUFBTUMsY0FBY0MsT0FBT0MsVUFBVSxDQUFDLGdDQUFnQ0MsT0FBTyxHQUFHLFNBQVM7UUFDekYsTUFBTUMsZUFBZVAsY0FBY0c7UUFFbkNOLFNBQVNVO1FBQ1RSLFdBQVc7SUFDYixHQUFHLEVBQUU7SUFFTCwwQkFBMEI7SUFDMUJULGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVEsU0FBUztZQUNYLE1BQU1VLE9BQU9DLFNBQVNDLGVBQWU7WUFDckMsSUFBSWQsVUFBVSxRQUFRO2dCQUNwQlksS0FBS0csU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDckIsT0FBTztnQkFDTEosS0FBS0csU0FBUyxDQUFDRSxNQUFNLENBQUM7WUFDeEI7WUFDQVosYUFBYWEsT0FBTyxDQUFDLFNBQVNsQjtRQUNoQztJQUNGLEdBQUc7UUFBQ0E7UUFBT0U7S0FBUTtJQUVuQixNQUFNaUIsY0FBYztRQUNsQmxCLFNBQVNtQixDQUFBQSxPQUFRQSxTQUFTLFVBQVUsU0FBUztJQUMvQztJQUVBLDZCQUE2QjtJQUM3QixJQUFJLENBQUNsQixTQUFTO1FBQ1oscUJBQU87c0JBQUdIOztJQUNaO0lBRUEscUJBQ0UsOERBQUNILGFBQWF5QixRQUFRO1FBQUNDLE9BQU87WUFBRXRCO1lBQU9tQjtRQUFZO2tCQUNoRHBCOzs7Ozs7QUFHUDtBQUVPLFNBQVN3QjtJQUNkLE1BQU1DLFVBQVUvQixpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSTRCLFlBQVkzQixXQUFXO1FBQ3pCLDZEQUE2RDtRQUM3RCxPQUFPO1lBQ0xHLE9BQU87WUFDUG1CLGFBQWEsS0FBTztRQUN0QjtJQUNGO0lBQ0EsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeD82ZjQ3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxudHlwZSBUaGVtZSA9ICdsaWdodCcgfCAnZGFyayc7XG5cbmludGVyZmFjZSBUaGVtZUNvbnRleHRUeXBlIHtcbiAgdGhlbWU6IFRoZW1lO1xuICB0b2dnbGVUaGVtZTogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgVGhlbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUaGVtZUNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFt0aGVtZSwgc2V0VGhlbWVdID0gdXNlU3RhdGU8VGhlbWU+KCdsaWdodCcpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvciBzeXN0ZW0gcHJlZmVyZW5jZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKSBhcyBUaGVtZTtcbiAgICBjb25zdCBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcyA/ICdkYXJrJyA6ICdsaWdodCc7XG4gICAgY29uc3QgaW5pdGlhbFRoZW1lID0gc2F2ZWRUaGVtZSB8fCBzeXN0ZW1UaGVtZTtcbiAgICBcbiAgICBzZXRUaGVtZShpbml0aWFsVGhlbWUpO1xuICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChtb3VudGVkKSB7XG4gICAgICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgICAgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcbiAgICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByb290LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKTtcbiAgICAgIH1cbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0aGVtZScsIHRoZW1lKTtcbiAgICB9XG4gIH0sIFt0aGVtZSwgbW91bnRlZF0pO1xuXG4gIGNvbnN0IHRvZ2dsZVRoZW1lID0gKCkgPT4ge1xuICAgIHNldFRoZW1lKHByZXYgPT4gcHJldiA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCcpO1xuICB9O1xuXG4gIC8vIFByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoXG4gIGlmICghbW91bnRlZCkge1xuICAgIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgdGhlbWUsIHRvZ2dsZVRoZW1lIH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVGhlbWUoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRoZW1lQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICAvLyBSZXR1cm4gZGVmYXVsdCB2YWx1ZXMgaW5zdGVhZCBvZiB0aHJvd2luZyBlcnJvciBkdXJpbmcgU1NSXG4gICAgcmV0dXJuIHtcbiAgICAgIHRoZW1lOiAnbGlnaHQnIGFzIFRoZW1lLFxuICAgICAgdG9nZ2xlVGhlbWU6ICgpID0+IHt9XG4gICAgfTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiVGhlbWVDb250ZXh0IiwidW5kZWZpbmVkIiwiVGhlbWVQcm92aWRlciIsImNoaWxkcmVuIiwidGhlbWUiLCJzZXRUaGVtZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzeXN0ZW1UaGVtZSIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJtYXRjaGVzIiwiaW5pdGlhbFRoZW1lIiwicm9vdCIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwiYWRkIiwicmVtb3ZlIiwic2V0SXRlbSIsInRvZ2dsZVRoZW1lIiwicHJldiIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VUaGVtZSIsImNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            provider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        resetAll: ()=>set(initialState),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e93baefb0a7b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU5M2JhZWZiMGE3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9zdHlsZXMvZ2xhc3MtZWZmZWN0cy5jc3M/NWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMGIxZjA0YzRlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ContextKit - AI Context Builder\",\n    description: \"Create organized, actionable context for AI-driven projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();