"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: function() { return /* binding */ useContextStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(\"Provider with id \".concat(provider.id, \" already exists\"));\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            provider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        resetAll: ()=>set(initialState),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/contextStore.ts\n"));

/***/ })

});