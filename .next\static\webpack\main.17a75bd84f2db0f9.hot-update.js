"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createKey: function() {\n        return createKey;\n    },\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || false) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        });\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            });\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    const data = await options.fetchData();\n    const effect = await getMiddlewareData(data.dataHref, data.response, options);\n    return {\n        dataHref: data.dataHref,\n        json: data.json,\n        response: data.response,\n        text: data.text,\n        cacheKey: data.cacheKey,\n        effect\n    };\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    const getData = (params)=>{\n        var _params_method;\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            for (const curAs of [\n                as,\n                resolvedAs\n            ]){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, \"http://n\").pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, \"http://n\").pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split(\"/\");\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark(\"routeChange\");\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit(\"routeChangeError\", buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit(\"hashChangeStart\", as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit(\"hashChangeComplete\", as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = \"replaceState\";\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith(\"/\") && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== \"/_error\") {\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(\", \") + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(\", \") + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\")));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit(\"routeChangeStart\", as, routeProps);\n        }\n        const isErrorRoute = this.pathname === \"/404\" || this.pathname === \"/_error\";\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if (\"route\" in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if (\"type\" in routeInfo) {\n                if (routeInfo.type === \"redirect-internal\") {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent(\"/404\");\n                        notFoundRoute = \"/404\";\n                    } catch (_) {\n                        notFoundRoute = \"/_error\";\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === \"/_error\" && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if (\"type\" in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === \"/_error\" && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        console.error(err);\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit(\"routeChangeError\", err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent(\"/_error\");\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? \"/404\" : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === \"redirect-internal\" || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === \"redirect-external\") {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === \"rewrite\") {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get(\"x-middleware-skip\");\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\", 2);\n        const [newUrlNoHash, newHash] = as.split(\"#\", 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\", 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === \"\" || hash === \"top\") {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState(\"replaceState\", (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n            const routerFilterSValue = {\"numItems\":14,\"errorRate\":0.0001,\"numBits\":269,\"numHashes\":14,\"bitArray\":[0,1,0,1,1,1,0,0,1,1,1,1,0,1,0,1,1,1,1,0,1,1,0,1,1,1,0,1,1,0,0,1,0,1,1,1,1,1,0,1,1,1,1,1,1,0,0,1,0,1,0,0,1,0,0,0,0,1,0,0,1,1,1,0,1,1,1,1,1,1,0,0,0,0,0,0,1,0,0,1,0,1,0,1,0,1,1,1,0,1,0,1,0,1,1,1,0,0,1,1,1,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,1,0,0,0,1,0,0,1,1,0,1,0,1,0,0,1,1,0,1,1,0,1,0,0,1,0,0,0,0,0,1,0,0,1,1,1,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,0,1,1,1,0,1,0,1,1,1,0,0,0,0,1,0,1,1,0,0,0,0,0,0,1,1,1,1,0,1,1,1,1,0,1,1,1,1,0,1,0,1,0,1,0,1,1,1,0,1,0,1,0,0,1,0,1,1,0,0,0,1,1,0,0,0,0,0,1,0,1,1,1,0,1,0,0,0,0,0,0,1,1,1,1,0,1,1,1,0]};\n            const staticFilterData = routerFilterSValue ? routerFilterSValue : undefined;\n            const routerFilterDValue = {\"numItems\":0,\"errorRate\":0.0001,\"numBits\":0,\"numHashes\":null,\"bitArray\":[]};\n            const dynamicFilterData = routerFilterDValue ? routerFilterDValue : undefined;\n            if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});