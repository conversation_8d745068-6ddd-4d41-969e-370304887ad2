import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import "../styles/glass-effects.css";
import { ThemeProvider } from "@/components/ThemeProvider";

export const metadata: Metadata = {
  title: "ContextKit - AI Context Builder",
  description: "Create organized, actionable context for AI-driven projects",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="antialiased font-arabic">
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
