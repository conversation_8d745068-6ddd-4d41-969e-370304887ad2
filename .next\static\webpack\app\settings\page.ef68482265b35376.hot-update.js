"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Save,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _apiSettings_globalSettings, _apiSettings_globalSettings1, _apiSettings_globalSettings2, _apiSettings_globalSettings3;\n    _s();\n    const { currentLanguage, apiSettings, setApiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider, getActiveProviders } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingModels, setEditingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        save: isArabic ? \"حفظ\" : \"Save\",\n        delete: isArabic ? \"حذف\" : \"Delete\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        globalSettings: isArabic ? \"الإعدادات العامة\" : \"Global Settings\",\n        temperature: isArabic ? \"درجة الحرارة\" : \"Temperature\",\n        topP: isArabic ? \"Top P\" : \"Top P\",\n        maxTokens: isArabic ? \"الحد الأقصى للرموز\" : \"Max Tokens\",\n        timeout: isArabic ? \"مهلة الاتصال (ثانية)\" : \"Timeout (seconds)\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\"\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\"); // مسح رسائل الخطأ السابقة\n        if (!selectedProviderId) {\n            setErrorMessage(isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\");\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\");\n            return;\n        }\n        // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\");\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\"\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\"); // مسح رسالة الخطأ عند النجاح\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\");\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-arabic\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                    children: translations.providers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setShowAddProvider(true);\n                                                        setErrorMessage(\"\");\n                                                        setSelectedProviderId(\"\");\n                                                    },\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        translations.addProvider\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-4\",\n                                        children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                                    children: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this) : configuredProviders.map((provider)=>{\n                                            const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(provider.id);\n                                            const validationState = validationStates[provider.id];\n                                            if (!providerInfo) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: providerInfo.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                                children: providerInfo.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 260,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                                children: providerInfo.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>removeProvider(provider.id),\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                                        children: translations.apiKey\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                value: provider.apiKey,\n                                                                                onChange: (e)=>updateProvider(provider.id, {\n                                                                                        apiKey: e.target.value\n                                                                                    }),\n                                                                                placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                className: \"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowKeys((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: !prev[provider.id]\n                                                                                        })),\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                                children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 297,\n                                                                                    columnNumber: 58\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 297,\n                                                                                    columnNumber: 91\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 293,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                                        children: translations.baseUrl\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: provider.baseUrl || providerInfo.baseUrl,\n                                                                        onChange: (e)=>updateProvider(provider.id, {\n                                                                                baseUrl: e.target.value\n                                                                            }),\n                                                                        placeholder: providerInfo.baseUrl,\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: [\n                                                                        translations.models,\n                                                                        \": \",\n                                                                        providerInfo.models.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleValidateProvider(provider.id),\n                                                                disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                className: \"flex items-center gap-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 rounded text-sm \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                        children: validationState.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, provider.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                children: translations.globalSettings\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: translations.temperature\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"0\",\n                                                            max: \"2\",\n                                                            step: \"0.1\",\n                                                            value: ((_apiSettings_globalSettings = apiSettings.globalSettings) === null || _apiSettings_globalSettings === void 0 ? void 0 : _apiSettings_globalSettings.temperature) || 0.7,\n                                                            onChange: (e)=>{\n                                                                const newSettings = {\n                                                                    ...apiSettings,\n                                                                    globalSettings: {\n                                                                        ...apiSettings.globalSettings,\n                                                                        temperature: parseFloat(e.target.value)\n                                                                    }\n                                                                };\n                                                                setApiSettings(newSettings);\n                                                            },\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: ((_apiSettings_globalSettings1 = apiSettings.globalSettings) === null || _apiSettings_globalSettings1 === void 0 ? void 0 : _apiSettings_globalSettings1.temperature) || 0.7\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: translations.topP\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"0\",\n                                                            max: \"1\",\n                                                            step: \"0.1\",\n                                                            value: ((_apiSettings_globalSettings2 = apiSettings.globalSettings) === null || _apiSettings_globalSettings2 === void 0 ? void 0 : _apiSettings_globalSettings2.topP) || 0.9,\n                                                            onChange: (e)=>{\n                                                                const newSettings = {\n                                                                    ...apiSettings,\n                                                                    globalSettings: {\n                                                                        ...apiSettings.globalSettings,\n                                                                        topP: parseFloat(e.target.value)\n                                                                    }\n                                                                };\n                                                                setApiSettings(newSettings);\n                                                            },\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: ((_apiSettings_globalSettings3 = apiSettings.globalSettings) === null || _apiSettings_globalSettings3 === void 0 ? void 0 : _apiSettings_globalSettings3.topP) || 0.9\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this),\n                                        translations.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Save_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-700 dark:text-red-300 font-arabic\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"+iOKHizZaG0dlElMS9xH+nhDIuY=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});