"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isArabic = currentLanguage === \"ar\";\n    const activeProviders = getActiveProviders();\n    const hasValidProvider = activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        setGeneratedSuggestions([]);\n        setShowSuggestions(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.8,\n                maxTokens: 500\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // تقسيم النتيجة إلى اقتراحات متعددة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                setGeneratedSuggestions(suggestions);\n                if (suggestions.length === 0) {\n                    setGeneratedSuggestions([\n                        isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\"\n                    ]);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            setGeneratedSuggestions([\n                \"\".concat(translations.error, \": \").concat(errorMessage)\n            ]);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        const fieldPrompts = {\n            name: {\n                ar: \"اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:\\n- قصيرة وسهلة التذكر\\n- تعكس طبيعة المشروع\\n- مناسبة للجمهور المستهدف\\n- أصلية ومميزة\",\n                en: \"Suggest 3 creative and suitable project names. The names should be:\\n- Short and memorable\\n- Reflect the project nature\\n- Suitable for target audience\\n- Original and distinctive\"\n            },\n            purpose: {\n                ar: \"اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:\\n- واضحة ومباشرة\\n- تركز على القيمة المضافة\\n- تجذب المستخدمين المستهدفين\\n- تميز المشروع عن المنافسين\",\n                en: \"Write 3 different versions describing the project purpose. They should be:\\n- Clear and direct\\n- Focus on added value\\n- Attract target users\\n- Differentiate from competitors\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:\\n- الخصائص الديموغرافية\\n- الاحتياجات والتحديات\\n- السلوكيات والتفضيلات\",\n                en: \"Define 3 different target user groups. For each group mention:\\n- Demographic characteristics\\n- Needs and challenges\\n- Behaviors and preferences\"\n            },\n            goals: {\n                ar: \"اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:\\n- محددة وقابلة للقياس\\n- قابلة للتحقيق وواقعية\\n- مرتبطة بالجدول الزمني\\n- تدعم رؤية المشروع\",\n                en: \"Suggest 3 sets of goals (short, medium, long-term). They should be:\\n- Specific and measurable\\n- Achievable and realistic\\n- Time-bound\\n- Support project vision\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح 3 خيارات مختلفة لـ \".concat(fieldName) : \"Suggest 3 different options for \".concat(fieldName);\n        const contextInfo = isArabic ? \"السياق الحالي للمشروع:\\n\".concat(JSON.stringify(context, null, 2), \"\\n\\n\") : \"Current project context:\\n\".concat(JSON.stringify(context, null, 2), \"\\n\\n\");\n        const currentValueInfo = currentValue ? isArabic ? \"القيمة الحالية: \".concat(currentValue, \"\\n\\n\") : \"Current value: \".concat(currentValue, \"\\n\\n\") : \"\";\n        const instructions = isArabic ? \"تعليمات:\\n1. قدم 3 اقتراحات مختلفة ومتنوعة\\n2. رقم كل اقتراح (1، 2، 3)\\n3. اجعل كل اقتراح في سطر منفصل\\n4. استخدم اللغة العربية الواضحة\\n5. اعتمد على السياق المتوفر لتحسين الاقتراحات\" : \"Instructions:\\n1. Provide 3 different and diverse suggestions\\n2. Number each suggestion (1, 2, 3)\\n3. Put each suggestion on a separate line\\n4. Use clear English\\n5. Use the available context to improve suggestions\";\n        return \"\".concat(contextInfo).concat(currentValueInfo).concat(basePrompt, \"\\n\\n\").concat(instructions);\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const useSuggestion = (suggestion)=>{\n        onValueChange(suggestion);\n        setShowSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"\\n          relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out\\n          backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group\\n          \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80\\n               hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90\\n               text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25\\n               hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500\\n               cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \"\\n          \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \"\\n        \"),\n                title: hasValidProvider ? translations.generateWithAI : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.generateWithAI\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            showSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                    children: translations.suggestions\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowSuggestions(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-3\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 animate-spin text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600 dark:text-gray-400 font-arabic\",\n                                        children: translations.generating\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this) : generatedSuggestions.length > 0 ? generatedSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 dark:text-white mb-3 font-arabic leading-relaxed\",\n                                            children: suggestion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: _s1(()=>{\n                                                        _s1();\n                                                        return useSuggestion(suggestion);\n                                                    }, \"Tm3IhIaFQeTaErUPTEGljA7SgqU=\", false, function() {\n                                                        return [\n                                                            useSuggestion\n                                                        ];\n                                                    }),\n                                                    className: \"relative flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"w-3 h-3 group-hover:rotate-12 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                translations.useThis\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>copyToClipboard(suggestion, index),\n                                                    className: \"relative flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-1\",\n                                                            children: [\n                                                                copiedIndex === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-green-300 animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3 group-hover:scale-110 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                copiedIndex === index ? translations.copied : translations.copy\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                        children: translations.error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: generateSuggestions,\n                                        className: \"relative mt-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative\",\n                                                children: translations.tryAgain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this),\n                            generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-3 border-t border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: generateSuggestions,\n                                    className: \"relative w-full flex items-center justify-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 to-pink-600/80 hover:from-purple-600/90 hover:to-pink-700/90 text-white shadow-md hover:shadow-lg hover:shadow-purple-500/25 hover:scale-105 active:scale-95 font-arabic\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 21\n                                                }, this),\n                                                translations.regenerate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"6Ol0w6P5jFBgQYeauMSqFSH9E+U=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});