'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';

export default function TestAIGeneration() {
  const { getActiveProviders, currentLanguage } = useContextStore();
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  
  const isArabic = currentLanguage === 'ar';
  const activeProviders = getActiveProviders();
  const hasValidProvider = activeProviders.some(p => p.apiKey && p.validationStatus === 'valid');

  const testAIGeneration = async () => {
    if (!hasValidProvider) {
      setTestResult(isArabic ? 'لا يوجد مقدم خدمة صالح' : 'No valid provider found');
      return;
    }

    setIsLoading(true);
    setTestResult('');

    try {
      const provider = activeProviders.find(p => p.apiKey && p.validationStatus === 'valid');
      
      if (!provider) {
        throw new Error('No valid provider found');
      }

      console.log('Testing with provider:', provider);

      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerId: provider.id,
          apiKey: provider.apiKey,
          model: provider.selectedModels[0] || 'gpt-3.5-turbo',
          messages: [
            { 
              role: 'user', 
              content: isArabic 
                ? 'اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني'
                : 'Suggest 3 creative names for a mobile e-commerce app project'
            }
          ],
          context: { test: true },
          fieldName: 'test',
          language: currentLanguage,
          temperature: 0.8,
          maxTokens: 500
        })
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('API Result:', result);

      if (result.success) {
        setTestResult(result.content);
      } else {
        setTestResult(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Test error:', error);
      setTestResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        {isArabic ? 'اختبار توليد الذكاء الاصطناعي' : 'AI Generation Test'}
      </h3>
      
      <div className="space-y-4">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {isArabic ? 'مقدمي الخدمة النشطين:' : 'Active Providers:'} {activeProviders.length}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {isArabic ? 'مقدم خدمة صالح:' : 'Valid Provider:'} {hasValidProvider ? '✅' : '❌'}
          </p>
          {activeProviders.map(provider => (
            <div key={provider.id} className="text-xs text-gray-500 dark:text-gray-400">
              {provider.id}: {provider.validationStatus} - {provider.apiKey ? 'Has API Key' : 'No API Key'}
            </div>
          ))}
        </div>

        <button
          onClick={testAIGeneration}
          disabled={isLoading || !hasValidProvider}
          className="relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <span className="relative">
            {isLoading 
              ? (isArabic ? 'جاري الاختبار...' : 'Testing...') 
              : (isArabic ? 'اختبار التوليد' : 'Test Generation')
            }
          </span>
        </button>

        {testResult && (
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="font-medium mb-2 text-gray-900 dark:text-white">
              {isArabic ? 'النتيجة:' : 'Result:'}
            </h4>
            <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {testResult}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
